# 演示：什么时候需要f-string
'''
def demo_string_formatting():
    name = "小明"
    age = 25
    city = "北京"

    print("=== 三种字符串格式化方法对比 ===")

    # 方法1：普通字符串拼接（不推荐，麻烦）
    print("方法1 - 字符串拼接：")
    print("你好 " + name + "! 很高兴认识来自" + city + "的你，今年" + str(age) + "岁")

    # 方法2：f-string（推荐！简洁易读）
    print("\n方法2 - f-string：")
    print(f"你好 {name}! 很高兴认识来自{city}的你，今年{age}岁")

    # 方法3：format方法
    print("\n方法3 - format方法：")
    print("你好 {}! 很高兴认识来自{}的你，今年{}岁".format(name, city, age))

    print("\n=== 什么时候需要f？===")
    print("1. 不需要f - 纯文本：")
    print("Hello World")  # 没有变量，不需要f

    print("\n2. 需要f - 包含变量：")
    print(f"用户名是：{name}")  # 有变量，需要f

    print("\n3. f-string的高级用法：")
    print(f"出生年份：{2025 - age}")  # 可以在{}里做计算
    print(f"年龄保留1位小数：{age:.1f}")  # 格式化数字
    print(f"姓名大写：{name.upper()}")  # 调用方法
'''
# 你的原始程序
def main_program():
    print("\n=== 你的个人信息收集器 ===")
    name = input("请输入你的姓名：")
    age = input("请输入你的年龄：")
    city = input("请输入你的城市：")

    print(f"你好 {name}! 很高兴认识来自{city}的你，今年{age}岁真是美好的年纪！")
    birthday = 2025 - int(age)
    print(f"你出生在 {birthday} 年")

with open("user.txt","w") as f: f.write(f"姓名{name}\n年龄{age}\n城市{city}")