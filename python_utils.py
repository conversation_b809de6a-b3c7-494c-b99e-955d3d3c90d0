#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python工具函数库
作者：Python初学者
创建时间：2025-06-27
说明：这个文件包含了学习过程中创建的可重用工具函数
"""

import datetime
import os


def print_separator(title="", char="=", length=50):
    """
    打印分隔线，用于美化输出
    
    参数：
        title (str): 标题文字
        char (str): 分隔符字符
        length (int): 分隔线长度
    """
    if title:
        print(f"\n{char * length}")
        print(f"{title:^{length}}")
        print(f"{char * length}")
    else:
        print(f"{char * length}")


def get_current_time():
    """
    获取当前时间的格式化字符串
    
    返回：
        str: 格式化的当前时间
    """
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def log_learning_progress(lesson_name, status="完成"):
    """
    记录学习进度到控制台
    
    参数：
        lesson_name (str): 课程名称
        status (str): 完成状态
    """
    timestamp = get_current_time()
    print(f"📝 [{timestamp}] {lesson_name} - {status}")


def create_practice_template(title, objectives):
    """
    创建练习模板
    
    参数：
        title (str): 练习标题
        objectives (list): 学习目标列表
    
    返回：
        str: 格式化的练习模板
    """
    template = f'''
def practice_{title.lower().replace(" ", "_")}():
    """
    {title}
    学习目标：
'''
    
    for i, objective in enumerate(objectives, 1):
        template += f"    {i}. {objective}\n"
    
    template += '''    """
    print_separator("{}")
    
    # 在这里添加你的练习代码
    
    print("\\n✅ 练习完成！")
'''.format(title)
    
    return template


def validate_input(prompt, input_type=str, validator=None):
    """
    验证用户输入
    
    参数：
        prompt (str): 提示信息
        input_type (type): 期望的输入类型
        validator (function): 验证函数
    
    返回：
        输入的值（已转换为指定类型）
    """
    while True:
        try:
            user_input = input(prompt)
            value = input_type(user_input)
            
            if validator and not validator(value):
                print("❌ 输入不符合要求，请重新输入")
                continue
                
            return value
        except ValueError:
            print(f"❌ 请输入有效的{input_type.__name__}类型")


def display_code_example(title, code, explanation=""):
    """
    显示代码示例
    
    参数：
        title (str): 示例标题
        code (str): 代码内容
        explanation (str): 解释说明
    """
    print(f"\n💡 {title}")
    print("-" * 40)
    print(code)
    if explanation:
        print(f"\n📖 说明：{explanation}")
    print("-" * 40)


def check_python_version():
    """
    检查Python版本
    
    返回：
        str: Python版本信息
    """
    import sys
    version = sys.version_info
    return f"Python {version.major}.{version.minor}.{version.micro}"


def create_learning_summary(topics_learned, time_spent, next_goals):
    """
    创建学习总结
    
    参数：
        topics_learned (list): 已学习的主题
        time_spent (str): 花费时间
        next_goals (list): 下一步目标
    
    返回：
        str: 格式化的学习总结
    """
    summary = f"""
📊 学习总结 - {get_current_time()}
{'=' * 50}

✅ 已完成的学习内容：
"""
    
    for i, topic in enumerate(topics_learned, 1):
        summary += f"   {i}. {topic}\n"
    
    summary += f"\n⏰ 学习时间：{time_spent}\n"
    summary += "\n🎯 下一步学习目标：\n"
    
    for i, goal in enumerate(next_goals, 1):
        summary += f"   {i}. {goal}\n"
    
    summary += "\n" + "=" * 50
    return summary


def safe_divide(a, b):
    """
    安全除法运算（避免除零错误）
    
    参数：
        a (float): 被除数
        b (float): 除数
    
    返回：
        float or str: 计算结果或错误信息
    """
    try:
        return a / b
    except ZeroDivisionError:
        return "错误：不能除以零"
    except TypeError:
        return "错误：输入必须是数字"


def format_number(number, decimal_places=2):
    """
    格式化数字显示
    
    参数：
        number (float): 要格式化的数字
        decimal_places (int): 小数位数
    
    返回：
        str: 格式化后的数字字符串
    """
    try:
        return f"{number:.{decimal_places}f}"
    except (ValueError, TypeError):
        return "无效数字"


def interactive_menu(options):
    """
    创建交互式菜单
    
    参数：
        options (dict): 选项字典，键为选项编号，值为选项描述
    
    返回：
        str: 用户选择的选项编号
    """
    print("\n📋 请选择：")
    for key, value in options.items():
        print(f"   {key}. {value}")
    
    while True:
        choice = input("\n请输入选项编号：").strip()
        if choice in options:
            return choice
        else:
            print("❌ 无效选择，请重新输入")


# 示例使用
if __name__ == "__main__":
    print("🔧 Python工具函数库")
    print(f"🐍 当前Python版本：{check_python_version()}")
    
    # 演示一些工具函数
    print_separator("工具函数演示")
    
    # 时间函数演示
    print(f"📅 当前时间：{get_current_time()}")
    
    # 学习进度记录演示
    log_learning_progress("工具函数学习", "进行中")
    
    # 安全除法演示
    print(f"🧮 10 ÷ 3 = {safe_divide(10, 3)}")
    print(f"🧮 10 ÷ 0 = {safe_divide(10, 0)}")
    
    # 数字格式化演示
    print(f"🔢 格式化π：{format_number(3.14159, 3)}")
    
    print("\n✅ 工具函数库演示完成！")
